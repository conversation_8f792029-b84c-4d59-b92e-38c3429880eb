import React from "react";
import { useNavigate } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import {
  selectActiveTab,
  setActiveTab,
} from "../../redux/slices/adminDashboardSlice";
import { logout } from "../../redux/slices/authSlice";
import "../../styles/AdminSidebar.css";

// Icons
import { MdDashboard, MdPeople, MdVideoLibrary, MdRateReview } from "react-icons/md";
import { FaChartBar, FaFileAlt, FaCog, FaGavel, FaHandshake, FaClipboardList, FaShoppingCart } from "react-icons/fa";
import { IoLogOut } from "react-icons/io5";

const AdminSidebar = ({ isMobile = false, onItemClick }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const activeTab = useSelector(selectActiveTab);

  // Define disabled sections
  const disabledSections = ["settings", "requests", "reports"];

  // Handle tab click
  const handleTabClick = (tab) => {
    // Prevent navigation for disabled sections
    if (disabledSections.includes(tab)) {
      return;
    }

    dispatch(setActiveTab(tab));

    // Navigate to the corresponding route
    switch (tab) {
      case "dashboard":
        navigate("/admin/dashboard");
        break;
      case "users":
        navigate("/admin/users");
        break;
      case "content":
        navigate("/admin/content");
        break;
      case "bids":
        navigate("/admin/bids");
        break;
      case "offers":
        navigate("/admin/offers");
        break;
      case "orders":
        navigate("/admin/orders");
        break;
      case "reviews":
        navigate("/admin/reviews");
        break;
      case "cms":
        navigate("/admin/cms");
        break;
      default:
        navigate("/admin/dashboard");
    }

    // Close mobile sidebar after navigation
    if (isMobile && onItemClick) {
      onItemClick();
    }
  };

  // Handle logout
  const handleLogout = () => {
    dispatch(logout());
    navigate("/");
  };

  // Coming Soon Badge Component
  const ComingSoonBadge = () => (
    <span className="coming-soon-badge">
      Coming Soon
    </span>
  );

  return (
    <div className={`AdminSidebar ${isMobile ? 'mobile' : ''}`}>
      <div className="AdminSidebar__container">
        {/* Logo Section - Hide on mobile as it's shown in mobile menu header */}
        {!isMobile && (
          <div className="AdminSidebar__logo">
            <h3>XOSportsHub</h3>
            <span>Admin Panel</span>
          </div>
        )}

        {/* Navigation Menu */}
        <ul className="AdminSidebar__menu">
          <li
            className={`AdminSidebar__item ${activeTab === "dashboard" ? "active" : ""
              }`}
            onClick={() => handleTabClick("dashboard")}
            data-tooltip="Dashboard Overview"
          >
            <div className="item-content">
              <MdDashboard className="AdminSidebar__icon" />
              <span>Dashboard Overview</span>
            </div>
          </li>

          <li
            className={`AdminSidebar__item ${activeTab === "users" ? "active" : ""
              }`}
            onClick={() => handleTabClick("users")}
            data-tooltip="User Management"
          >
            <div className="item-content">
              <MdPeople className="AdminSidebar__icon" />
              <span>User Management</span>
            </div>
          </li>

          <li
            className={`AdminSidebar__item ${activeTab === "content" ? "active" : ""
              }`}
            onClick={() => handleTabClick("content")}
            data-tooltip="Content Management"
          >
            <div className="item-content">
              <MdVideoLibrary className="AdminSidebar__icon" />
              <span>Content Management</span>
            </div>
          </li>

          <li
            className={`AdminSidebar__item ${activeTab === "bids" ? "active" : ""
              }`}
            onClick={() => handleTabClick("bids")}
            data-tooltip="Bid Management"
          >
            <div className="item-content">
              <FaGavel className="AdminSidebar__icon" />
              <span>Bid Management</span>
            </div>
          </li>

          <li
            className={`AdminSidebar__item ${activeTab === "offers" ? "active" : ""
              }`}
            onClick={() => handleTabClick("offers")}
            data-tooltip="Offer Management"
          >
            <div className="item-content">
              <FaHandshake className="AdminSidebar__icon" />
              <span>Offer Management</span>
            </div>
          </li>

          <li
            className={`AdminSidebar__item ${activeTab === "orders" ? "active" : ""
              }`}
            onClick={() => handleTabClick("orders")}
            data-tooltip="Order Management"
          >
            <div className="item-content">
              <FaShoppingCart className="AdminSidebar__icon" />
              <span>Order Management</span>
            </div>
          </li>
          <li
            className={`AdminSidebar__item ${activeTab === "reviews" ? "active" : ""
              }`}
            onClick={() => handleTabClick("reviews")}
            data-tooltip="Review Management"
          >
            <div className="item-content">
              <MdRateReview className="AdminSidebar__icon" />
              <span>Review Management</span>
            </div>
          </li>
          <li
            className={`AdminSidebar__item ${activeTab === "cms" ? "active" : ""
              }`}
            onClick={() => handleTabClick("cms")}
            data-tooltip="CMS Pages"
          >
            <div className="item-content">
              <FaFileAlt className="AdminSidebar__icon" />
              <span>CMS Pages</span>
            </div>
          </li>
          <li
            className={`AdminSidebar__item ${activeTab === "requests" ? "active" : ""} ${disabledSections.includes("requests") ? "disabled" : ""}`}
            onClick={() => handleTabClick("requests")}
            data-tooltip="Request Management"
          >
            <div className="item-content">
              <FaClipboardList className="AdminSidebar__icon" />
              <span>Request Management</span>
            </div>
            {disabledSections.includes("requests") && <ComingSoonBadge />}
          </li>

          

          <li
            className={`AdminSidebar__item ${activeTab === "reports" ? "active" : ""} ${disabledSections.includes("reports") ? "disabled" : ""}`}
            onClick={() => handleTabClick("reports")}
            data-tooltip="Reports & Analytics"
          >
            <div className="item-content">
              <FaChartBar className="AdminSidebar__icon" />
              <span>Reports & Analytics</span>
            </div>
            {disabledSections.includes("reports") && <ComingSoonBadge />}
          </li>

          

          <li
            className={`AdminSidebar__item ${activeTab === "settings" ? "active" : ""} ${disabledSections.includes("settings") ? "disabled" : ""}`}
            onClick={() => handleTabClick("settings")}
            data-tooltip="Settings"
          >
            <div className="item-content">
              <FaCog className="AdminSidebar__icon" />
              <span>Settings</span>
            </div>
            {disabledSections.includes("settings") && <ComingSoonBadge />}
          </li>
        </ul>

        {/* Logout Section - Hide on mobile as it's shown in mobile menu footer */}
        {!isMobile && (
          <div className="AdminSidebar__logout">
            <div
              className="AdminSidebar__item logout-item"
              onClick={handleLogout}
              data-tooltip="Logout"
            >
              <div className="item-content">
                <IoLogOut className="AdminSidebar__icon" />
                <span>Logout</span>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdminSidebar;
