/* AdminLayout Component Styles */
.AdminLayout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--admin-bg);
  position: relative;
}

.AdminLayout__container {
  display: flex;
  flex: 1;
  position: relative;
  padding-top: var(--admin-navbar-height); /* Account for fixed navbar height */
}

/* Sidebar */
.AdminLayout__sidebar {
  width: var(--admin-sidebar-width);
  background-color: var(--admin-card-bg);
  border-right: 1px solid var(--admin-border);
  position: fixed;
  top: var(--admin-navbar-height); /* Position below fixed navbar */
  left: 0;
  height: calc(100vh - var(--admin-navbar-height)); /* Full height minus navbar */
  z-index: var(--z-index-sidebar);
  transition: all var(--transition-bounce);
  overflow-y: auto;
  box-shadow: var(--admin-shadow-md);
}

.AdminLayout__sidebar.collapsed {
  width: var(--admin-sidebar-width-collapsed);
}

/* Mobile Hamburger Menu Styles */
.AdminLayout__mobile-menu {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--admin-card-bg);
  overflow-y: auto;
}

.mobile-menu-header {
  padding: var(--admin-padding-lg);
  border-bottom: 1px solid var(--admin-border);
  background-color: var(--admin-bg);
}

.mobile-menu-logo h3 {
  color: var(--primary-color);
  font-size: var(--admin-subtitle-size);
  font-weight: 700;
  margin: 0;
}

.mobile-menu-logo span {
  color: var(--admin-text-secondary);
  font-size: var(--admin-caption-size);
  font-weight: 500;
}

.mobile-menu-profile {
  padding: var(--admin-padding-lg);
  border-bottom: 1px solid var(--admin-border);
  display: flex;
  align-items: center;
  gap: var(--admin-margin-sm);
}

.mobile-profile-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  background-color: var(--admin-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.mobile-profile-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.mobile-profile-avatar svg {
  font-size: 24px;
  color: var(--admin-text-secondary);
}

.mobile-profile-info h4 {
  margin: 0;
  font-size: var(--admin-body-size);
  font-weight: 600;
  color: var(--admin-text-primary);
}

.mobile-profile-info p {
  margin: 2px 0;
  font-size: var(--admin-caption-size);
  color: var(--admin-text-secondary);
}

.mobile-profile-role {
  font-size: var(--admin-caption-size);
  color: var(--btn-color);
  font-weight: 500;
}

.mobile-menu-actions {
  padding: var(--admin-padding-sm) 0;
  border-bottom: 1px solid var(--admin-border);
}

.mobile-menu-action {
  width: 100%;
  display: flex;
  align-items: center;
  gap: var(--admin-margin-sm);
  padding: var(--admin-padding-sm) var(--admin-padding-lg);
  border: none;
  background: none;
  color: var(--admin-text-primary);
  font-size: var(--admin-body-size);
  cursor: pointer;
  transition: background-color var(--transition-normal);
}

.mobile-menu-action:hover {
  background-color: var(--admin-hover);
}

.mobile-menu-action .action-icon {
  font-size: var(--admin-icon-size);
  color: var(--admin-text-secondary);
}

.mobile-menu-nav {
  flex: 1;
  overflow-y: auto;
}

.mobile-menu-logout {
  padding: var(--admin-padding-lg);
  border-top: 1px solid var(--admin-border);
  background-color: var(--admin-bg);
}

.mobile-logout-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--admin-margin-sm);
  padding: var(--admin-padding-sm);
  border: 1px solid var(--error-color);
  border-radius: var(--admin-border-radius);
  background-color: transparent;
  color: var(--error-color);
  font-size: var(--admin-body-size);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.mobile-logout-btn:hover {
  background-color: var(--error-color);
  color: var(--white);
}

.mobile-logout-btn .logout-icon {
  font-size: var(--admin-icon-size);
}

.AdminLayout__sidebar::-webkit-scrollbar {
  width: 4px;
}

.AdminLayout__sidebar::-webkit-scrollbar-track {
  background: var(--bg-gray);
}

.AdminLayout__sidebar::-webkit-scrollbar-thumb {
  background: var(--light-gray);
  border-radius: 2px;
}

.AdminLayout__sidebar::-webkit-scrollbar-thumb:hover {
  background: var(--dark-gray);
}

/* Main Content */
.AdminLayout__main {
  flex: 1;
  margin-left: var(--admin-sidebar-width);
  padding: var(--admin-padding-xl) var(--admin-padding-lg);
  transition: margin-left var(--transition-bounce);
  min-height: calc(100vh - var(--admin-navbar-height));
  overflow-y: auto;
  position: relative;
  background-color: var(--admin-bg);
}

.AdminLayout__main.sidebar-collapsed {
  margin-left: var(--admin-sidebar-width-collapsed);
}

/* Breadcrumb Navigation */
.AdminLayout__breadcrumb {
  margin-bottom: var(--admin-margin-md);
}

.breadcrumb-nav {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  font-size: var(--admin-caption-size);
  color: var(--admin-text-secondary);
}

.breadcrumb-item {
  color: var(--admin-text-secondary);
  transition: color var(--transition-normal);
}

.breadcrumb-item:last-child {
  color: var(--btn-color);
  font-weight: 600;
}

.breadcrumb-separator {
  margin: 0 var(--space-sm);
  color: var(--admin-text-muted);
}

/* Page Header */
.AdminLayout__header {
  background-color: var(--admin-card-bg);
  border-radius: var(--admin-border-radius);
  box-shadow: var(--admin-shadow-sm);
  padding: var(--admin-padding-lg);
  margin-bottom: var(--admin-margin-lg);
  border: 1px solid var(--admin-border);
}

.AdminLayout__title {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  margin: 0;
  font-size: var(--admin-title-size);
  font-weight: 700;
  color: var(--admin-text-primary);
}

.AdminLayout__title-icon {
  font-size: var(--admin-title-size);
  color: var(--btn-color);
}

/* Page Content */
.AdminLayout__content {
  background-color: var(--admin-card-bg);
  border-radius: var(--admin-border-radius);
  box-shadow: var(--admin-shadow-sm);
  padding: var(--admin-padding-lg);
  border: 1px solid var(--admin-border);
}

/* Tablet and Mobile Responsive */
@media (max-width: 1024px) {
  .AdminLayout__container {
    padding-top: 65px; /* Slightly smaller navbar on tablet */
  }

  .AdminLayout__sidebar {
    top: 65px;
    height: calc(100vh - 65px);
  }
}

/* Tablet Responsive Design */
@media (max-width: 1024px) and (min-width: 769px) {
  .AdminLayout__sidebar {
    width: 240px;
  }

  .AdminLayout__main {
    margin-left: 240px;
    padding: var(--heading6) var(--basefont);
  }

  .AdminLayout__main.sidebar-collapsed {
    margin-left: 70px;
  }

  .AdminLayout__sidebar.collapsed {
    width: 70px;
  }

  .AdminLayout__breadcrumb {
    margin-bottom: var(--smallfont);
  }

  .breadcrumb-nav {
    font-size: var(--smallfont);
  }
}

/* Mobile Sidebar */
@media (max-width: 768px) {
  .AdminLayout__container {
    padding-top: var(--admin-navbar-height-mobile);
  }

  .AdminLayout__sidebar {
    width: 100vw;
    left: -100vw; /* Hidden by default */
    top: var(--admin-navbar-height-mobile);
    height: calc(100vh - var(--admin-navbar-height-mobile));
    z-index: calc(var(--z-index-sidebar) + 100); /* Higher z-index for mobile overlay */
    box-shadow: var(--admin-shadow-lg);
    transition: left var(--transition-bounce);
  }

  .AdminLayout__sidebar.mobile-open {
    left: 0;
    transform: translateX(0);
  }

  .AdminLayout__sidebar.collapsed {
    width: 100vw; /* Full width on mobile even when "collapsed" */
    left: -100vw;
  }

  .AdminLayout__main {
    margin-left: 0;
    padding: var(--admin-padding-sm);
    min-height: calc(100vh - var(--admin-navbar-height-mobile));
  }

  .AdminLayout__main.sidebar-collapsed {
    margin-left: 0;
  }

  .AdminLayout__overlay {
    position: fixed;
    top: var(--admin-navbar-height-mobile); /* Start below navbar */
    left: 0;
    width: 100%;
    height: calc(100vh - var(--admin-navbar-height-mobile));
    background-color: rgba(0, 0, 0, 0.5);
    z-index: var(--z-index-overlay); /* Between sidebar and content */
    backdrop-filter: blur(2px);
  }

  .AdminLayout__header {
    padding: var(--admin-padding-sm);
  }

  .AdminLayout__title {
    font-size: var(--admin-subtitle-size);
  }

  .AdminLayout__title-icon {
    font-size: var(--admin-subtitle-size);
  }

  .AdminLayout__content {
    padding: var(--admin-padding-sm);
  }

  /* Mobile Menu Responsive Adjustments */
  .mobile-menu-header {
    padding: var(--admin-padding-md);
  }

  .mobile-menu-profile {
    padding: var(--admin-padding-md);
  }

  .mobile-profile-avatar {
    width: 50px;
    height: 50px;
  }

  .mobile-menu-logout {
    padding: var(--admin-padding-md);
  }
}

@media (max-width: 480px) {
  .AdminLayout__container {
    padding-top: var(--admin-navbar-height-mobile);
  }

  .AdminLayout__sidebar {
    top: var(--admin-navbar-height-mobile);
    height: calc(100vh - var(--admin-navbar-height-mobile));
    width: 100vw; /* Full width on very small screens */
    left: -100vw;
  }

  .AdminLayout__sidebar.mobile-open {
    left: 0;
  }

  .AdminLayout__overlay {
    top: var(--admin-navbar-height-mobile);
    height: calc(100vh - var(--admin-navbar-height-mobile));
  }

  .AdminLayout__main {
    padding: var(--admin-padding-xs);
    min-height: calc(100vh - var(--admin-navbar-height-mobile));
  }

  .AdminLayout__breadcrumb {
    margin-bottom: var(--admin-margin-xs);
  }

  .breadcrumb-nav {
    font-size: var(--tinyfont);
  }

  .AdminLayout__header {
    padding: var(--admin-padding-xs);
    margin-bottom: var(--admin-margin-xs);
  }

  .AdminLayout__title {
    font-size: var(--admin-body-size);
  }

  .AdminLayout__title-icon {
    font-size: var(--admin-body-size);
  }

  .AdminLayout__content {
    padding: var(--admin-padding-xs);
  }

  /* Small Mobile Menu Adjustments */
  .mobile-menu-header {
    padding: var(--admin-padding-sm);
  }

  .mobile-menu-profile {
    padding: var(--admin-padding-sm);
  }

  .mobile-profile-avatar {
    width: 45px;
    height: 45px;
  }

  .mobile-profile-info h4 {
    font-size: var(--admin-caption-size);
  }

  .mobile-profile-info p,
  .mobile-profile-role {
    font-size: var(--tinyfont);
  }

  .mobile-menu-logout {
    padding: var(--admin-padding-sm);
  }
}

/* Smooth transitions */
.AdminLayout__sidebar,
.AdminLayout__main {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Focus states for accessibility */
.breadcrumb-item:focus {
  outline: 2px solid var(--btn-color);
  outline-offset: 2px;
  border-radius: var(--border-radius);
}

/* Loading states */
.AdminLayout__content.loading {
  position: relative;
  overflow: hidden;
}

.AdminLayout__content.loading::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Error states */
.AdminLayout__content.error {
  border: 1px solid #ff6b6b;
  background-color: #fff5f5;
}

/* Success states */
.AdminLayout__content.success {
  border: 1px solid #51cf66;
  background-color: #f3fff3;
}

/* Custom scrollbar for main content */
.AdminLayout__content::-webkit-scrollbar {
  width: 6px;
}

.AdminLayout__content::-webkit-scrollbar-track {
  background: var(--bg-gray);
  border-radius: 3px;
}

.AdminLayout__content::-webkit-scrollbar-thumb {
  background: var(--light-gray);
  border-radius: 3px;
}

.AdminLayout__content::-webkit-scrollbar-thumb:hover {
  background: var(--dark-gray);
}
